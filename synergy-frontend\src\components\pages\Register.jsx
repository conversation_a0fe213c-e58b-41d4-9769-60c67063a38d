import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import AuthCandidateRegisterForm from "../../ui/formik-form/AuthCandidateRegisterForm.jsx";

const Register = () => {
  const navigate = useNavigate();
  
  const [formData, setFormData] = useState({
    first_name: "",
    last_name: "",
    name: "",
    email: "",
    number: "",
    gender: "",
    date_of_birth: "",
    nationality: "",
    password: "",
    confirm_password: "",
    occupation: "",
    referral: "",
  });

  const [errorMessage, setErrorMessage] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [showEmailPopup, setShowEmailPopup] = useState(false);
  const [errors, setErrors] = useState({});

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  const handleRegister = async (e) => {
    e.preventDefault();
    // Validate required fields
    if (Object.values(formData).some((value) => !value)) {
      setErrorMessage("All fields are required!");
      setErrors({});
      return;
    }

    // Validate password match
    if (formData.password !== formData.confirm_password) {
      setErrorMessage("Passwords do not match!");
      setErrors({});
      return;
    }

    // Validate date of birth (must be in the past)
    const enteredDate = new Date(formData.date_of_birth);
    const today = new Date();
    if (enteredDate >= today) {
      setErrorMessage("Date of Birth must be a past date.");
      setErrors({});
      return;
    }

    setErrorMessage("");
    setSuccessMessage("");
    setErrors({});

    try {
      const response = await fetch(`${import.meta.env.VITE_HOST_URL}/candidate-register`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (!response.ok) {
        // Map backend errors to field-level errors
        let fieldErrors = {};
        if (data.errors) {
          if (data.errors.email) fieldErrors.email = data.errors.email[0];
          if (data.errors.number) fieldErrors.number = data.errors.number[0];
        }
        setErrors(fieldErrors);
        throw new Error(data.message || "Registration failed");
      }

      setSuccessMessage("Registration successful! Redirecting to login...");

      if (data.token) {
        localStorage.setItem("authToken", data.token);
      }

      setFormData({
        first_name: "",
        last_name: "",
        name: "",
        email: "",
        number: "",
        gender: "",
        date_of_birth: "",
        nationality: "",
        password: "",
        confirm_password: "",
        occupation: "",
        referral: "",
      });

      // Show the email popup after successful registration
      setShowEmailPopup(true);
      
      // Redirect to login page after a short delay (2 seconds)
      setTimeout(() => {
        navigate("/login");
      }, 2000);
      
    } catch (error) {
      console.error("Error:", error);
      setErrorMessage(error.message || "Something went wrong!");
    }
  };

  return (
    <div className="flex h-screen bg-white mt-56">
      <div className="w-full md:w-[800px] h-[600px] flex justify-center items-center bg-white bg-opacity-90 rounded-lg shadow-xl mx-auto overflow-y-auto">
        <div className="w-full">
          <h2 className="text-3xl font-semibold text-center text-gray-800 mb-2">Create Account</h2>

          {errorMessage && (
            <div className="text-red-500 text-center mb-4">
              <p>{errorMessage}</p>
            </div>
          )}

          {successMessage && (
            <div className="text-green-500 text-center mb-4">
              <p>{successMessage}</p>
            </div>
          )}

          <div className="mt-8">
            <AuthCandidateRegisterForm
              formData={formData}
              handleChange={handleChange}
              errors={errors}
              onSubmit={handleRegister}
            />
          </div>

          <div className="flex justify-center mt-4">
            <button
              onClick={handleRegister}
              type="submit"
              className="w-56 text-center py-3 bg-blue-500 text-white font-semibold rounded-lg hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 transition"
            >
              Register
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Register;