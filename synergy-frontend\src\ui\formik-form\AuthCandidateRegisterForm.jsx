import React, { useState, useEffect } from "react";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/style.css";

const AuthCandidateRegisterForm = ({ formData, handleChange, onSubmit, errors }) => {
  const [passwordError, setPasswordError] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [phoneError, setPhoneError] = useState("");

  const inputClass =
    "peer h-12 w-full border-b-2 border-gray-300 text-gray-900 placeholder-transparent focus:outline-none focus:border-purple-600 px-2";
  const labelClass =
    "absolute left-0 -top-3.5 text-sm text-gray-600 transition-all peer-placeholder-shown:top-2.5 peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-400 peer-focus:-top-3.5 peer-focus:text-sm peer-focus:text-purple-600";

  const handlePasswordChange = (e) => {
    handleChange(e);

    if (formData.confirm_password && e.target.value !== formData.confirm_password) {
      setPasswordError("Passwords do not match");
    } else {
      setPasswordError("");
    }
  };

  const handleConfirmPasswordChange = (e) => {
    handleChange(e);

    if (e.target.value !== formData.password) {
      setPasswordError("Passwords do not match");
    } else {
      setPasswordError("");
    }
  };

  // Get only the digits from a phone number and extract the national number if it's a Bangladesh number
  const extractBDNationalNumber = (phoneValue) => {
    if (!phoneValue) return { isBD: false, nationalDigits: "" };
    
    // Extract only digits
    const allDigits = phoneValue.replace(/\D/g, '');
    
    // Check if it's a Bangladesh number
    const isBD = allDigits.startsWith('880');
    
    // Extract national number (digits after country code)
    const nationalDigits = isBD ? allDigits.substring(3) : "";
    
    return { isBD, nationalDigits };
  };

  const handlePhoneChange = (value, data) => {
    // Get clean digits and check if it's Bangladesh
    const allDigits = value.replace(/\D/g, '');
    const isBD = allDigits.startsWith('880');
    
    // For Bangladesh (+880), enforce exactly 10 digits after country code
    if (isBD) {
      const nationalNumber = allDigits.substring(3); // Get digits after '880'
      
      // If national number is longer than 10 digits, truncate it
      if (nationalNumber.length > 10) {
        // Keep only the first 10 digits after country code
        const truncatedValue = '880' + nationalNumber.substring(0, 10);
        // Format the number to reflect in the input
        const formattedNumber = `+${truncatedValue.substring(0, 3)} ${truncatedValue.substring(3)}`;
        handleChange({ target: { name: "number", value: formattedNumber } });
        setPhoneError("Bangladesh numbers must be exactly 10 digits after country code");
        return;
      } else {
        setPhoneError(nationalNumber.length < 10 && nationalNumber.length > 0 ? 
          "Bangladesh numbers must be exactly 10 digits after country code" : "");
      }
    } else {
      setPhoneError("");
    }
    
    handleChange({ target: { name: "number", value } });
  };

  // Validate phone number on form submission and when number changes
  useEffect(() => {
    if (formData.number) {
      const allDigits = formData.number.replace(/\D/g, '');
      
      if (allDigits.startsWith('880')) {
        const nationalNumber = allDigits.substring(3); // Get digits after '880'
        setPhoneError(nationalNumber.length !== 10 ? 
          "Bangladesh numbers must be exactly 10 digits after country code" : "");
      }
    }
  }, [formData.number]);

  const handleSubmit = (e) => {
    e.preventDefault();

    // Final validation before submission
    if (formData.password !== formData.confirm_password) {
      setPasswordError("Passwords do not match");
      return;
    }

    // Validate Bangladesh phone number
    if (formData.number) {
      const allDigits = formData.number.replace(/\D/g, '');
      
      if (allDigits.startsWith('880')) {
        const nationalNumber = allDigits.substring(3); // Get digits after '880'
        if (nationalNumber.length !== 10) {
          setPhoneError("Bangladesh numbers must be exactly 10 digits after country code");
          return;
        }
      }
    }

    onSubmit(e);
  };

  // Style to hide browser's default password reveal button
  // const hidePasswordRevealStyles = `
  //   /* For WebKit browsers (Chrome, Safari) */
  //   input::-webkit-credentials-auto-fill-button,
  //   input::-webkit-inner-spin-button,
  //   input::-webkit-outer-spin-button,
  //   input::-webkit-search-cancel-button,
  //   input::-webkit-search-decoration,
  //   input::-webkit-search-results-button,
  //   input::-webkit-search-results-decoration,
  //   input::-webkit-contacts-auto-fill-button,
  //   input::-webkit-caps-lock-indicator,
  //   input::-webkit-text-fill-color-preview,
  //   input::-webkit-textfield-decoration-container {
  //     visibility: hidden !important;
  //     display: none !important;
  //     pointer-events: none !important;
  //   }

  //   /* For Microsoft Edge */
  //   input::-ms-reveal,
  //   input::-ms-clear {
  //     display: none !important;
  //   }

  //   /* For Mozilla Firefox */
  //   input[type="password"] {
  //     -moz-appearance: textfield !important;
  //   }

  //   /* Mobile optimizations */
  //   @media (max-width: 768px) {
  //     input, select {
  //       font-size: 16px !important; /* Prevents iOS zoom on focus */
  //     }
      
  //     .react-tel-input .form-control {
  //       font-size: 16px !important;
  //       height: 48px !important;
  //     }
      
  //     .react-tel-input .flag-dropdown {
  //       border: none !important;
  //       background: transparent !important;
  //     }
      
  //     .react-tel-input .selected-flag {
  //       padding: 0 8px 0 12px !important;
  //     }
      
  //     /* Increase touch target size */
  //     button {
  //       min-height: 44px;
  //       min-width: 44px;
  //     }
  //   }
  // `;

  // For monitoring keystrokes in phone input for Bangladesh numbers
  const handlePhoneKeyDown = (event) => {
    // Skip if not a character key (like arrows, delete, etc.)
    if (event.ctrlKey || event.metaKey || event.altKey || 
        ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab', 'Home', 'End'].includes(event.key)) {
      return;
    }
    
    // Check if it's a Bangladesh number
    const phoneValue = event.target.value;
    const allDigits = phoneValue.replace(/\D/g, '');
    
    if (allDigits.startsWith('880')) {
      const nationalNumber = allDigits.substring(3);
      
      // Only allow input if national number is less than 10 digits
      if (nationalNumber.length >= 10 && /\d/.test(event.key)) {
        event.preventDefault();
      }
    }
  };

  return (
    <div className="w-full mx-auto p-4 sm:p-6 md:p-8">
      {/* Add the style to hide browser's default password reveal button */}
      {/* <style>{hidePasswordRevealStyles}</style> */}
      
      {errors?.original?.error && (
        <div className="mb-6 p-3 bg-red-100 border-l-4 border-red-500 text-red-700 text-sm md:text-base">
          <p>{errors.original.error}</p>
        </div>
      )}

      <form className="space-y-6 md:space-y-8" onSubmit={handleSubmit}>
        {/* first name and last name */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
          <div className="relative mb-2">
            <input
              type="text"
              name="first_name"
              value={formData.first_name || ''}
              onChange={(e) => {
                // Only allow letters and spaces
                const value = e.target.value;
                if (value === '' || /^[A-Za-z\s]*$/.test(value)) {
                  handleChange(e);
                }
              }}
              placeholder="First Name"
              pattern="^[A-Za-z\s]+$"
              title="Only letters and spaces are allowed"
              inputMode="text"
              required
              className={inputClass}
            />
            <label htmlFor="first_name" className={labelClass}>First Name</label>
          </div>
          <div className="relative mb-2">
            <input
              type="text"
              name="last_name"
              value={formData.last_name || ''}
              onChange={(e) => {
                // Only allow letters and spaces
                const value = e.target.value;
                if (value === '' || /^[A-Za-z\s]*$/.test(value)) {
                  handleChange(e);
                }
              }}
              placeholder="Last Name"
              pattern="^[A-Za-z\s]+$"
              title="Only letters and spaces are allowed"
              inputMode="text"
              required
              className={inputClass}
            />
            <label htmlFor="last_name" className={labelClass}>Last Name</label>
          </div>
        </div>
        
        {/* Preferred Name and Email */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
          <div className="relative mb-2">
            <input
              type="text"
              name="name"
              value={formData.name || ''}
              onChange={(e) => {
                // Only allow letters and spaces
                const value = e.target.value;
                if (value === '' || /^[A-Za-z\s]*$/.test(value)) {
                  handleChange(e);
                }
              }}
              placeholder="Preferred Name"
              pattern="^[A-Za-z\s]+$"
              title="Only letters and spaces are allowed"
              inputMode="text"
              required
              className={inputClass}
            />
            <label htmlFor="name" className={labelClass}>Preferred Name</label>
          </div>
          <div className="relative mb-2">
            <input
              type="email"
              name="email"
              value={formData.email || ''}
              onChange={handleChange}
              placeholder="Email"
              inputMode="email"
              className={inputClass}
              required
            />
            <label htmlFor="email" className={labelClass}>Email</label>
            {errors?.email && (
              <p className="text-red-500 text-xs mt-1">{errors.email}</p>
            )}
          </div>
        </div>
          

        {/* Gender and DOB */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
          <div className="relative mb-2">
            <select
              name="gender"
              value={formData.gender || ''}
              onChange={handleChange}
              required
              className="h-12 w-full border-b-2 border-gray-300 text-gray-700 bg-transparent focus:outline-none focus:border-purple-600 px-2"
            >
              <option value="">Select Gender</option>
              <option value="Male">Male</option>
              <option value="Female">Female</option>
              <option value="Other">Other</option>
              <option value="Prefer not to say">Prefer not to say</option>
            </select>
            <label className="text-sm text-gray-600 mt-1 block">Gender</label>
          </div>
          <div className="relative mb-2">
            <input
              type="date"
              name="date_of_birth"
              value={formData.date_of_birth || ''}
              onChange={handleChange}
              required
              max={new Date().toISOString().split('T')[0]} // Prevent future dates
              className="h-12 w-full border-b-2 border-gray-300 text-gray-700 bg-transparent focus:outline-none focus:border-purple-600 px-2"
            />
            <label className="text-sm text-gray-600 mt-1 block">Date of Birth</label>
          </div>
        </div>

        {/* Phone and Nationality */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
          <div className="relative mb-2">
            <div className="phone-input-container h-12">
              <PhoneInput
                country={"us"}
                value={formData.number || ''}
                onChange={handlePhoneChange}
                inputProps={{
                  name: "number",
                  required: true,
                  className: "peer h-12 w-full border-b-2 border-gray-300 text-gray-900 focus:outline-none focus:border-purple-600 pl-16",
                  onKeyDown: handlePhoneKeyDown,
                  style: { fontSize: '16px' }, // Prevent iOS zoom
                }}
                containerStyle={{
                  width: "100%",
                  position: "relative",
                }}
                buttonStyle={{
                  border: "none",
                  background: "transparent",
                  position: "absolute",
                  top: "50%",
                  left: "0",
                  transform: "translateY(-50%)",
                }}
                dropdownStyle={{
                  width: "300px", // Wider dropdown for mobile
                  maxHeight: "300px",
                }}
              />
            </div>
            <label htmlFor="number" className="absolute left-0 -top-3.5 text-sm text-gray-600 transition-all peer-placeholder-shown:top-2.5 peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-400 peer-focus:-top-3.5 peer-focus:text-sm peer-focus:text-purple-600">
              Phone Number
            </label>
            {phoneError && (
              <p className="text-red-500 text-xs mt-1">{phoneError}</p>
            )}
            {errors?.number && (
              <p className="text-red-500 text-xs mt-1">{errors.number}</p>
            )}
          </div>

          <div className="relative mb-2">
            <input
              list="nationality-list"
              type="text"
              name="nationality"
              value={formData.nationality || ''}
              onChange={handleChange}
              placeholder="Nationality"
              pattern="[A-Za-z\s]*"
              title="Only letters and spaces are allowed"
              inputMode="text"
              required
              className={inputClass}
            />
            <label htmlFor="nationality" className={labelClass}>Nationality</label>
            <datalist id="nationality-list">
              {[
                "Afghan", "Albanian", "Algerian", "American", "Andorran", "Angolan",
                "Argentine", "Australian", "Bangladeshi", "British", "Canadian",
                "Chinese", "French", "German", "Indian", "Italian", "Japanese",
                "Mexican", "Nigerian", "Pakistani", "Russian", "Saudi",
                "South African", "Spanish", "Sri Lankan", "Turkish", "Vietnamese",
                "Zimbabwean"
              ].sort().map((nation) => (
                <option key={nation} value={nation} />
              ))}
            </datalist>
          </div>
        </div>

        {/* Occupation and Referral */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
          <div className="relative mb-2">
            <input
              type="text"
              name="occupation"
              value={formData.occupation || ''}
              onChange={handleChange}
              placeholder="Occupation"
              inputMode="text"
              required
              className={inputClass}
            />
            <label htmlFor="occupation" className={labelClass}>Occupation</label>
          </div>

          <div className="relative mb-2">
            <input
              type="text"
              name="referral"
              list="referralOptions"
              value={formData.referral || ''}
              onChange={handleChange}
              placeholder="How did you find us?"
              className={inputClass}
              required
            />
            <label htmlFor="referral" className={labelClass}>Referral</label>

            {/* Datalist allows both dropdown and free typing */}
            <datalist id="referralOptions">
              <option value="Friends and Family" />
              <option value="CV Portals" />
              <option value="Website" />
              <option value="Social Media" />
              <option value="Other" />
            </datalist>
          </div>


        </div>
        

        {/* Passwords */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
          <div className="relative mb-2">
            <div className="flex items-center">
              <input
                type={showPassword ? "text" : "password"}
                name="password"
                value={formData.password || ''}
                onChange={handlePasswordChange}
                placeholder="Password"
                className={`${inputClass} pr-12`}
                required
                minLength="8"
                autoComplete="new-password"
                style={{ fontSize: '16px' }} // Prevent iOS zoom
              />
              <button
                type="button"
                className="absolute right-2 w-10 h-10 flex items-center justify-center text-gray-500 hover:text-purple-600"
                onClick={() => setShowPassword(!showPassword)}
                aria-label={showPassword ? "Hide password" : "Show password"}
              >
                {showPassword ? (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                    <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clipRule="evenodd" />
                    <path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z" />
                  </svg>
                )}
              </button>
            </div>
            <label htmlFor="password" className={labelClass}>Password (min 8 characters)</label>
          </div>
          <div className="relative mb-2">
            <div className="flex items-center relative">
              <input
                type={showConfirmPassword ? "text" : "password"}
                name="confirm_password"
                value={formData.confirm_password || ''}
                onChange={handleConfirmPasswordChange}
                placeholder="Confirm Password"
                className={`${inputClass} pr-12`}
                required
                autoComplete="new-password"
                style={{ fontSize: '16px' }} // Prevent iOS zoom
              />
              <button
                type="button"
                className="absolute right-2 w-10 h-10 flex items-center justify-center text-gray-500 hover:text-purple-600"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                aria-label={showConfirmPassword ? "Hide confirm password" : "Show confirm password"}
              >
                {showConfirmPassword ? (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                    <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clipRule="evenodd" />
                    <path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z" />
                  </svg>
                )}
              </button>
            </div>
            <label htmlFor="confirm_password" className={labelClass}>Confirm Password</label>
            {(passwordError || errors?.original?.error) && (
              <p className="text-red-500 text-xs mt-1">{passwordError || errors.original.error}</p>
            )}
          </div>
        </div>

        {/* Submit Button */}
      
      </form>
    </div>
  );
};

export default AuthCandidateRegisterForm;